import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

interface UseActivateItemParams {
    userItemId: number;
}

/**
 * Custom hook to use an item
 */
export const useActivateItem = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        // eslint-disable-next-line react-hooks/react-compiler
        orpc.user.useItem.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.INVENTORY });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.STATUSEFFECTS });
                toast.success("Item used successfully!");

                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error: Error) => {
                toast.error(error.message || "Failed to use item");
            },
        })
    );
};

export default useActivateItem;
